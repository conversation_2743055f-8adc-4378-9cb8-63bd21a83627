package com.example.repository;

import com.example.entity.Photo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
/**
 * 照片数据访问接口，用于定义与照片相关的数据库操作。
 */
public interface PhotoRepository extends JpaRepository<Photo, Long> {
    /**
     * 查询所有未被删除且公开的照片。
     * @return 未被删除且公开的照片列表。
     */
    List<Photo> findByDeletedFalseAndIsPublicTrue();

    /**
     * 根据文件名查询未被删除的照片。
     * @param fileName 照片的文件名。
     * @return 匹配的未被删除的照片。
     */
    Photo findByFileNameAndDeletedFalse(String fileName);

    /**
     * 根据上传IP地址查询未被删除的照片。
     * @param ip 上传照片的IP地址。
     * @return 匹配的未被删除的照片列表。
     */
    List<Photo> findByUploadIpAndDeletedFalse(String ip);

    /**
     * 根据上传时间范围查询未被删除的照片。
     * @param start 起始时间。
     * @param end 结束时间。
     * @return 在指定时间内上传的、未被删除的照片列表。
     */
    List<Photo> findByUploadTimeBetweenAndDeletedFalse(LocalDateTime start, LocalDateTime end);
}
